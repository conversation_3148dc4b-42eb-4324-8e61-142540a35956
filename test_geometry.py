#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试几何参数计算
"""

from hexagon_simulation import HexagonSimulation
import numpy as np

def test_geometry_calculation():
    """测试几何参数计算"""
    print("测试几何参数计算...")
    
    # 创建仿真实例
    sim = HexagonSimulation(
        rotation_center_x_offset=2.0,
        film_thickness=0.1,
        total_rotation=720,
        step_angle=1.0,
        use_rounded_corners=True,
        sharp_radius=0.8,
        blunt_radius=12.0
    )
    
    print('初始几何参数已计算完成')
    print(f'边长: {[f"{l:.2f}" for l in sim.initial_geometry["edge_lengths"]]}')
    print(f'圆弧长度: {[f"{l:.2f}" for l in sim.initial_geometry["arc_lengths"]]}')
    print(f'Upper到V5距离: {sim.initial_geometry["upper_to_v5_distance"]:.2f}mm')
    
    # 测试边长增量计算
    print("\n测试边长增量计算:")
    test_cases = [
        ('V5', 'V4'),
        ('V4', 'V3'),
        ('V3', 'V2'),
        ('V2', 'V1'),
        ('V1', 'V6'),
        ('V6', 'V5'),
    ]
    
    for prev, curr in test_cases:
        increment = sim._get_edge_increment(prev, curr)
        print(f'{prev} -> {curr}: {increment:.2f}mm')

if __name__ == "__main__":
    test_geometry_calculation()
