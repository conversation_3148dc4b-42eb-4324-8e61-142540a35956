#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试切点计算
分析V5和V4的切点位置和距离
"""

import numpy as np
import matplotlib.pyplot as plt
from hexagon_simulation import HexagonSimulation

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False

def debug_tangent_points():
    """调试V5和V4的切点计算"""
    print("=" * 60)
    print("调试V5和V4的切点计算")
    print("=" * 60)
    
    # 创建仿真
    sim = HexagonSimulation(
        rotation_center_x_offset=2.0,
        use_rounded_corners=True,
        sharp_radius=0.8,
        blunt_radius=12.0,
        film_thickness=0.1,
    )
    
    # 获取原始顶点
    vertices = sim.original_vertices
    print("原始顶点坐标:")
    for i, vertex in enumerate(vertices):
        print(f"V{i+1}: ({vertex[0]:8.3f}, {vertex[1]:8.3f})")
    
    # 计算V5和V4之间的直线距离
    v5_vertex = vertices[4]  # V5 (索引4)
    v4_vertex = vertices[3]  # V4 (索引3)
    direct_distance = np.linalg.norm(v4_vertex - v5_vertex)
    print(f"\nV5-V4直线距离: {direct_distance:.3f}mm")
    
    # 获取圆心和半径
    v5_center = sim.rounded_corner_calc.circle_centers[4]
    v4_center = sim.rounded_corner_calc.circle_centers[3]
    v5_radius = sim.rounded_corner_calc.blunt_radius  # V5是钝角
    v4_radius = sim.rounded_corner_calc.sharp_radius  # V4是锐角
    
    print(f"\nV5圆心: ({v5_center[0]:8.3f}, {v5_center[1]:8.3f}), 半径: {v5_radius:.3f}mm")
    print(f"V4圆心: ({v4_center[0]:8.3f}, {v4_center[1]:8.3f}), 半径: {v4_radius:.3f}mm")
    
    # 计算V5的切点
    v5_tangent1, v5_tangent2 = sim.rounded_corner_calc.calculate_tangent_points_on_edges(
        v5_center, v5_radius, 4, vertices
    )
    print(f"\nV5切点 (当前实现):")
    print(f"  tangent1 (V4-V5边): ({v5_tangent1[0]:8.3f}, {v5_tangent1[1]:8.3f})")
    print(f"  tangent2 (V5-V6边): ({v5_tangent2[0]:8.3f}, {v5_tangent2[1]:8.3f})")

    # 计算V4的切点
    v4_tangent1, v4_tangent2 = sim.rounded_corner_calc.calculate_tangent_points_on_edges(
        v4_center, v4_radius, 3, vertices
    )
    print(f"\nV4切点:")
    print(f"  tangent1 (V5-V4边): ({v4_tangent1[0]:8.3f}, {v4_tangent1[1]:8.3f})")
    print(f"  tangent2 (V4-V3边): ({v4_tangent2[0]:8.3f}, {v4_tangent2[1]:8.3f})")

    # 修正V5的切点计算 - 需要找到V5圆与V4-V5边的正确交点
    print(f"\n=== 修正V5切点计算 ===")

    def calculate_correct_tangent_point(center, radius, point1, point2):
        """
        正确计算内切圆与直线的切点
        切点应该是圆心到直线的垂足
        """
        # 直线方向向量
        line_vec = point2 - point1
        line_length = np.linalg.norm(line_vec)

        if line_length < 1e-10:
            return point1

        # 单位方向向量
        line_unit = line_vec / line_length

        # 从point1到圆心的向量
        to_center = center - point1

        # 计算投影长度（圆心在直线上的投影）
        proj_length = np.dot(to_center, line_unit)

        # 投影点就是切点（圆心到直线的垂足）
        tangent_point = point1 + proj_length * line_unit

        return tangent_point

    # 使用修正的方法计算V5圆心到V4-V5边的切点
    v5_correct_tangent = calculate_correct_tangent_point(
        v5_center, v5_radius, v4_vertex, v5_vertex
    )
    print(f"V5修正切点 (V4-V5边): ({v5_correct_tangent[0]:8.3f}, {v5_correct_tangent[1]:8.3f})")

    # 验证修正后的切点是否在V4-V5边上
    v4_to_v5 = v5_vertex - v4_vertex
    v4_to_correct_tangent = v5_correct_tangent - v4_vertex

    if np.linalg.norm(v4_to_v5) > 0:
        projection_ratio_correct = np.dot(v4_to_correct_tangent, v4_to_v5) / np.dot(v4_to_v5, v4_to_v5)
        print(f"修正切点在V4-V5边上的投影比例: {projection_ratio_correct:.3f}")
        print(f"(0表示在V4处，1表示在V5处)")

        # 验证切点是否真的在线段上
        if 0 <= projection_ratio_correct <= 1:
            print("✅ 切点在V4-V5线段上")
        else:
            print("❌ 切点不在V4-V5线段上")

    # 验证圆心到切点的距离
    dist_center_to_tangent = np.linalg.norm(v5_correct_tangent - v5_center)
    print(f"V5圆心到修正切点的距离: {dist_center_to_tangent:.3f}mm")

    # 验证切点到直线的距离（应该为0）
    line_vec = v5_vertex - v4_vertex
    line_unit = line_vec / np.linalg.norm(line_vec)
    to_tangent = v5_correct_tangent - v4_vertex
    proj_length = np.dot(to_tangent, line_unit)
    proj_point = v4_vertex + proj_length * line_unit
    dist_to_line = np.linalg.norm(v5_correct_tangent - proj_point)
    print(f"修正切点到V4-V5直线的距离: {dist_to_line:.6f}mm (应该为0)")

    # 计算修正后的切点距离
    tangent_distance_correct = np.linalg.norm(v4_tangent1 - v5_correct_tangent)
    print(f"\n修正后的切点距离 (V5_correct -> V4_tangent1): {tangent_distance_correct:.3f}mm")

    # 原始错误的距离计算
    tangent_distance = np.linalg.norm(v4_tangent1 - v5_tangent1)
    print(f"原始错误的切点距离 (V5_tangent1 -> V4_tangent1): {tangent_distance:.3f}mm")
    
    # 验证切点是否在正确的边上
    # V5_tangent2应该在V5-V4边上
    v5_to_v4 = v4_vertex - v5_vertex
    v5_to_tangent2 = v5_tangent2 - v5_vertex

    # 计算投影比例
    if np.linalg.norm(v5_to_v4) > 0:
        projection_ratio = np.dot(v5_to_tangent2, v5_to_v4) / np.dot(v5_to_v4, v5_to_v4)
        print(f"\n原始V5_tangent2在V5-V4边上的投影比例: {projection_ratio:.3f}")
        print(f"(0表示在V5处，1表示在V4处)")

    # V4_tangent1也应该在V5-V4边上
    v4_to_tangent1 = v4_tangent1 - v5_vertex  # 从V5开始计算
    if np.linalg.norm(v5_to_v4) > 0:
        projection_ratio2 = np.dot(v4_to_tangent1, v5_to_v4) / np.dot(v5_to_v4, v5_to_v4)
        print(f"V4_tangent1在V5-V4边上的投影比例: {projection_ratio2:.3f}")

    # 分析距离差异
    print(f"\n距离分析:")
    print(f"期望距离: 6.46mm")
    print(f"原始错误距离: {tangent_distance:.3f}mm")
    print(f"修正后距离: {tangent_distance_correct:.3f}mm")
    print(f"原始差异: {tangent_distance - 6.46:+.3f}mm")
    print(f"修正后差异: {tangent_distance_correct - 6.46:+.3f}mm")
    
    # 检查切点计算是否正确
    # V5_tangent2到V5圆心的距离应该等于半径
    v5_tangent2_to_center = np.linalg.norm(v5_tangent2 - v5_center)
    v4_tangent1_to_center = np.linalg.norm(v4_tangent1 - v4_center)
    v5_correct_tangent_to_center = np.linalg.norm(v5_correct_tangent - v5_center)

    print(f"\n切点验证:")
    print(f"V5_tangent2到V5圆心距离: {v5_tangent2_to_center:.3f}mm (应该是{v5_radius:.3f}mm)")
    print(f"V5_correct_tangent到V5圆心距离: {v5_correct_tangent_to_center:.3f}mm (应该是{v5_radius:.3f}mm)")
    print(f"V4_tangent1到V4圆心距离: {v4_tangent1_to_center:.3f}mm (应该是{v4_radius:.3f}mm)")

    return {
        'vertices': vertices,
        'v5_center': v5_center,
        'v4_center': v4_center,
        'v5_tangent1': v5_tangent1,
        'v5_tangent2': v5_tangent2,
        'v5_correct_tangent': v5_correct_tangent,  # 添加修正后的切点
        'v4_tangent1': v4_tangent1,
        'v4_tangent2': v4_tangent2,
        'direct_distance': direct_distance,
        'tangent_distance': tangent_distance,
        'tangent_distance_correct': tangent_distance_correct  # 添加修正后的距离
    }

def create_visualization(data):
    """创建可视化图表"""
    print("\n" + "=" * 60)
    print("创建切点可视化")
    print("=" * 60)
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    # 绘制六边形轮廓
    vertices = data['vertices']
    hex_x = np.append(vertices[:, 0], vertices[0, 0])
    hex_y = np.append(vertices[:, 1], vertices[0, 1])
    ax.plot(hex_x, hex_y, 'k-', linewidth=2, alpha=0.5, label='六边形轮廓')
    
    # 标记所有顶点
    for i, vertex in enumerate(vertices):
        ax.plot(vertex[0], vertex[1], 'ko', markersize=8)
        ax.text(vertex[0] + 2, vertex[1] + 2, f'V{i+1}', fontsize=12, fontweight='bold')
    
    # 突出显示V5和V4
    v5_vertex = vertices[4]
    v4_vertex = vertices[3]
    ax.plot(v5_vertex[0], v5_vertex[1], 'ro', markersize=12, label='V5')
    ax.plot(v4_vertex[0], v4_vertex[1], 'bo', markersize=12, label='V4')
    
    # 绘制V5-V4边
    ax.plot([v5_vertex[0], v4_vertex[0]], [v5_vertex[1], v4_vertex[1]], 
            'g-', linewidth=3, alpha=0.7, label='V5-V4边')
    
    # 绘制V5和V4的内切圆
    v5_center = data['v5_center']
    v4_center = data['v4_center']
    
    v5_circle = plt.Circle(v5_center, 12.0, fill=False, color='red',
                          linestyle='--', linewidth=2, alpha=0.7)
    v4_circle = plt.Circle(v4_center, 0.8, fill=False, color='blue',
                          linestyle='--', linewidth=2, alpha=0.7)
    ax.add_patch(v5_circle)
    ax.add_patch(v4_circle)
    
    # 标记圆心
    ax.plot(v5_center[0], v5_center[1], 'r+', markersize=10, markeredgewidth=2)
    ax.plot(v4_center[0], v4_center[1], 'b+', markersize=10, markeredgewidth=2)
    
    # 绘制切点
    v5_tangent2 = data['v5_tangent2']
    v5_correct_tangent = data['v5_correct_tangent']
    v4_tangent1 = data['v4_tangent1']

    # 绘制原始错误的切点
    ax.plot(v5_tangent2[0], v5_tangent2[1], 'rs', markersize=8, alpha=0.5, label='V5_tangent2 (错误)')

    # 绘制修正后的切点
    ax.plot(v5_correct_tangent[0], v5_correct_tangent[1], 'ro', markersize=10, label='V5_correct (修正)')
    ax.plot(v4_tangent1[0], v4_tangent1[1], 'bs', markersize=10, label='V4_tangent1')

    # 绘制错误的切点连线（灰色虚线）
    ax.plot([v5_tangent2[0], v4_tangent1[0]], [v5_tangent2[1], v4_tangent1[1]],
            'gray', linewidth=2, linestyle='--', alpha=0.5,
            label=f'错误距离 {data["tangent_distance"]:.3f}mm')

    # 绘制修正后的切点连线（绿色实线）
    ax.plot([v5_correct_tangent[0], v4_tangent1[0]], [v5_correct_tangent[1], v4_tangent1[1]],
            'green', linewidth=3,
            label=f'修正距离 {data["tangent_distance_correct"]:.3f}mm')

    # 绘制从圆心到切点的线（验证切点正确性）
    ax.plot([v5_center[0], v5_tangent2[0]], [v5_center[1], v5_tangent2[1]],
            'r:', alpha=0.3, linewidth=1)
    ax.plot([v5_center[0], v5_correct_tangent[0]], [v5_center[1], v5_correct_tangent[1]],
            'r-', alpha=0.7, linewidth=2)
    ax.plot([v4_center[0], v4_tangent1[0]], [v4_center[1], v4_tangent1[1]],
            'b-', alpha=0.7, linewidth=2)

    # 添加修正后距离标注
    mid_x = (v5_correct_tangent[0] + v4_tangent1[0]) / 2
    mid_y = (v5_correct_tangent[1] + v4_tangent1[1]) / 2
    ax.text(mid_x, mid_y + 3, f'修正: {data["tangent_distance_correct"]:.3f}mm',
           ha='center', va='bottom', fontsize=12, fontweight='bold',
           bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8))

    # 添加期望距离标注
    ax.text(mid_x, mid_y - 5, f'期望: 6.46mm',
           ha='center', va='top', fontsize=10, color='red',
           bbox=dict(boxstyle='round,pad=0.3', facecolor='pink', alpha=0.8))
    
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.legend()
    ax.set_title('V5-V4切点距离分析', fontsize=14, fontweight='bold')
    ax.set_xlabel('X坐标 (mm)')
    ax.set_ylabel('Y坐标 (mm)')
    
    plt.tight_layout()
    plt.show()

def analyze_edge_geometry():
    """分析边的几何特性"""
    print("\n" + "=" * 60)
    print("分析V5-V4边的几何特性")
    print("=" * 60)
    
    # 创建仿真
    sim = HexagonSimulation(
        rotation_center_x_offset=2.0,
        use_rounded_corners=True,
        sharp_radius=0.8,
        blunt_radius=12.0,
        film_thickness=0.1,
    )
    
    vertices = sim.original_vertices
    v5_vertex = vertices[4]
    v4_vertex = vertices[3]
    
    # 计算边长和角度
    edge_vector = v4_vertex - v5_vertex
    edge_length = np.linalg.norm(edge_vector)
    edge_angle = np.arctan2(edge_vector[1], edge_vector[0]) * 180 / np.pi
    
    print(f"V5-V4边分析:")
    print(f"V5坐标: ({v5_vertex[0]:8.3f}, {v5_vertex[1]:8.3f})")
    print(f"V4坐标: ({v4_vertex[0]:8.3f}, {v4_vertex[1]:8.3f})")
    print(f"边向量: ({edge_vector[0]:8.3f}, {edge_vector[1]:8.3f})")
    print(f"边长度: {edge_length:.3f}mm")
    print(f"边角度: {edge_angle:.1f}°")
    
    # 分析圆角对边长的影响
    v5_center = sim.rounded_corner_calc.circle_centers[4]
    v4_center = sim.rounded_corner_calc.circle_centers[3]
    radius = sim.rounded_corner_calc.blunt_radius
    
    # 计算圆心到边的距离
    # 边的方程：从V5到V4
    edge_unit = edge_vector / edge_length
    
    # V5圆心到边的距离
    v5_to_edge = v5_vertex - v5_center
    v5_dist_to_edge = abs(np.cross(v5_to_edge, edge_unit))
    
    # V4圆心到边的距离
    v4_to_edge = v4_vertex - v4_center
    v4_dist_to_edge = abs(np.cross(v4_to_edge, edge_unit))
    
    print(f"\n圆心到边的距离:")
    print(f"V5圆心到V5-V4边: {v5_dist_to_edge:.3f}mm (应该约等于半径{radius:.3f}mm)")
    print(f"V4圆心到V5-V4边: {v4_dist_to_edge:.3f}mm (应该约等于半径{radius:.3f}mm)")
    
    # 理论切点位置
    # 如果圆心到边的距离等于半径，那么切点就是圆心在边上的投影
    
    return edge_length

def main():
    """主函数"""
    print("V5-V4切点距离调试")
    print("=" * 60)
    
    try:
        # 调试切点计算
        data = debug_tangent_points()
        
        # 分析边几何
        edge_length = analyze_edge_geometry()
        
        # 创建可视化
        create_visualization(data)
        
        print("\n" + "=" * 60)
        print("调试完成!")
        print("=" * 60)
        
        print("关键发现:")
        print(f"✓ V5-V4直线距离: {data['direct_distance']:.3f}mm")
        print(f"✗ 原始错误切点距离: {data['tangent_distance']:.3f}mm")
        print(f"✓ 修正后切点距离: {data['tangent_distance_correct']:.3f}mm")
        print(f"✓ 期望距离: 6.46mm")
        print(f"✗ 原始差异: {data['tangent_distance'] - 6.46:+.3f}mm")
        print(f"✓ 修正后差异: {data['tangent_distance_correct'] - 6.46:+.3f}mm")

        if abs(data['tangent_distance_correct'] - 6.46) < 0.1:
            print("🎉 修正后的切点距离正确!")
        else:
            print("⚠️ 修正后的切点距离仍有问题，需要进一步分析")

        print(f"\n问题总结:")
        print(f"❌ 原始实现的问题: V5切点计算错误，没有正确找到与V4-V5边相交的点")
        print(f"✅ 修正方案: 直接计算V5圆心到V4-V5边的切点")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
